import sharp from 'sharp';
import { getLogoBuffer } from './logo';

export interface WatermarkOptions {
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' | 'center';
  opacity?: number;
  padding?: number;
  scale?: number;
}

export async function addWatermark(
  imageBuffer: Buffer,
  options: WatermarkOptions = {}
): Promise<Buffer> {
  const {
    position = 'bottom-right',
    padding = 20,
    scale = 0.15
  } = options;

  console.log('🎨 Starting watermark process...');
  console.log('📐 Watermark options:', { position, padding, scale });

  try {
    // Get the main image metadata
    const mainImage = sharp(imageBuffer);
    const { width, height } = await mainImage.metadata();

    if (!width || !height) {
      throw new Error('Unable to get image dimensions');
    }

    console.log('📏 Main image dimensions:', { width, height });

    // Get logo buffer
    console.log('🔍 Loading logo buffer...');
    const logoBuffer = await getLogoBuffer();
    console.log('✅ Logo buffer loaded, size:', logoBuffer.length, 'bytes');
    
    // Calculate logo dimensions based on main image size
    const logoWidth = Math.floor(width * scale);
    const logoHeight = Math.floor(logoWidth * 0.3); // Maintain aspect ratio

    console.log('📐 Calculated logo dimensions:', { logoWidth, logoHeight });

    // Resize logo and adjust opacity
    const resizedLogo = await sharp(logoBuffer)
      .resize(logoWidth, logoHeight, {
        fit: 'inside',
        withoutEnlargement: true
      })
      .png()
      .toBuffer();

    console.log('✅ Logo resized, new size:', resizedLogo.length, 'bytes');

    // Calculate position based on the chosen position
    let left: number;
    let top: number;

    switch (position) {
      case 'bottom-right':
        left = width - logoWidth - padding;
        top = height - logoHeight - padding;
        break;
      case 'bottom-left':
        left = padding;
        top = height - logoHeight - padding;
        break;
      case 'top-right':
        left = width - logoWidth - padding;
        top = padding;
        break;
      case 'top-left':
        left = padding;
        top = padding;
        break;
      case 'center':
        left = Math.floor((width - logoWidth) / 2);
        top = Math.floor((height - logoHeight) / 2);
        break;
      default:
        left = width - logoWidth - padding;
        top = height - logoHeight - padding;
    }

    console.log('📍 Logo position calculated:', { position, left, top, padding });

    // Create composite image with watermark
    const finalLeft = Math.max(0, left);
    const finalTop = Math.max(0, top);

    console.log('🔧 Applying watermark at position:', { finalLeft, finalTop });

    const watermarkedImage = await mainImage
      .composite([
        {
          input: resizedLogo,
          left: finalLeft,
          top: finalTop,
          blend: 'over'
        }
      ])
      .png()
      .toBuffer();

    console.log('✅ Watermark applied successfully, final image size:', watermarkedImage.length, 'bytes');
    return watermarkedImage;
  } catch (error) {
    console.error('Error adding watermark:', error);
    throw error;
  }
}

export function getWatermarkConfig(): WatermarkOptions {
  return {
    position: (process.env.WATERMARK_POSITION as WatermarkOptions['position']) || 'bottom-right',
    opacity: process.env.WATERMARK_OPACITY ? parseFloat(process.env.WATERMARK_OPACITY) : 0.8,
    padding: process.env.WATERMARK_PADDING ? parseInt(process.env.WATERMARK_PADDING) : 20,
    scale: process.env.WATERMARK_SCALE ? parseFloat(process.env.WATERMARK_SCALE) : 0.12
  };
}

export function isWatermarkEnabled(): boolean {
  return process.env.WATERMARK_ENABLED !== 'false';
}

export async function addWatermarkFromUrl(
  imageUrl: string,
  options: WatermarkOptions = {}
): Promise<Buffer> {
  console.log('🌐 Starting watermark from URL process...');
  console.log('🔗 Image URL:', imageUrl);

  try {
    // Check if watermarking is enabled
    const watermarkEnabled = isWatermarkEnabled();
    console.log('⚙️ Watermark enabled:', watermarkEnabled);

    if (!watermarkEnabled) {
      console.log('⚠️ Watermarking disabled, returning original image');
      // If disabled, just download and return the original image
      const response = await fetch(imageUrl);
      if (!response.ok) {
        throw new Error(`Failed to download image: ${response.statusText}`);
      }
      const originalBuffer = Buffer.from(await response.arrayBuffer());
      console.log('✅ Original image downloaded, size:', originalBuffer.length, 'bytes');
      return originalBuffer;
    }

    // Download the image
    console.log('⬇️ Downloading image from URL...');
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.statusText}`);
    }

    const imageBuffer = Buffer.from(await response.arrayBuffer());
    console.log('✅ Image downloaded, size:', imageBuffer.length, 'bytes');

    // Merge with environment config
    const envConfig = getWatermarkConfig();
    const finalOptions = { ...envConfig, ...options };
    console.log('⚙️ Final watermark options:', finalOptions);

    // Add watermark
    console.log('🎨 Applying watermark to downloaded image...');
    const watermarkedBuffer = await addWatermark(imageBuffer, finalOptions);
    console.log('✅ Watermark process completed successfully');

    return watermarkedBuffer;
  } catch (error) {
    console.error('Error downloading and watermarking image:', error);
    throw error;
  }
}