import OpenAI from "openai";
import { addWatermarkFromUrl } from "./watermark";
import { uploadImageToStorage } from "./supabase";

export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY!,
});

export interface ImageGenerationResult {
  success: boolean;
  imageUrl?: string;
  originalUrl?: string;
  error?: string;
}

export async function generateImage(prompt: string): Promise<ImageGenerationResult> {
  try {
    const response = await openai.images.generate({
      model: "dall-e-3", // Updated to use actual available model
      prompt: prompt,
      n: 1,
      size: "1024x1024",
      quality: "standard", // Updated to use correct quality parameter
    });

    const originalImageUrl = response.data?.[0]?.url;

    if (!originalImageUrl) {
      return {
        success: false,
        error: "No image URL returned from OpenAI"
      };
    }

    // Add watermark to the image
    try {
      const watermarkedBuffer = await addWatermarkFromUrl(originalImageUrl);

      // Generate unique filename
      const timestamp = Date.now();
      const filename = `watermarked-${timestamp}.png`;

      // Upload watermarked image to Supabase Storage
      const watermarkedUrl = await uploadImageToStorage(watermarkedBuffer, filename);

      if (watermarkedUrl) {
        return {
          success: true,
          imageUrl: watermarkedUrl,
          originalUrl: originalImageUrl
        };
      } else {
        // If watermarking fails, return original image
        console.warn('Failed to upload watermarked image, returning original');
        return {
          success: true,
          imageUrl: originalImageUrl,
          originalUrl: originalImageUrl
        };
      }
    } catch (watermarkError) {
      console.error('Watermarking failed:', watermarkError);
      // If watermarking fails, return original image
      return {
        success: true,
        imageUrl: originalImageUrl,
        originalUrl: originalImageUrl
      };
    }
  } catch (error) {
    console.error('OpenAI API error:', error);
    
    let errorMessage = "Failed to generate image";
    
    if (error instanceof OpenAI.APIError) {
      errorMessage = `OpenAI API Error: ${error.message}`;
    } else if (error instanceof Error) {
      errorMessage = error.message;
    }

    return {
      success: false,
      error: errorMessage
    };
  }
}