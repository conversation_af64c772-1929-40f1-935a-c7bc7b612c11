#!/usr/bin/env bun

/**
 * <PERSON>ript to test the webhook endpoint locally
 */

import { config } from "dotenv";

// Load environment variables
config({ path: ".env.local" });

const WEBHOOK_URL = process.env.WEBHOOK_URL || "http://localhost:3000/api/webhook/telegram";

async function testWebhookHealth() {
  console.log("🔍 Testing webhook health endpoint...");
  console.log(`📡 URL: ${WEBHOOK_URL}`);
  
  try {
    const response = await fetch(WEBHOOK_URL, {
      method: "GET",
    });
    
    const result = await response.json();
    
    if (response.ok) {
      console.log("✅ Webhook health check passed!");
      console.log("📋 Response:", result);
    } else {
      console.error("❌ Webhook health check failed:");
      console.error(`Status: ${response.status}`);
      console.error("Response:", result);
    }
  } catch (error) {
    console.error("❌ Error testing webhook:", error);
  }
}

async function testWebhookWithMockUpdate() {
  console.log("🧪 Testing webhook with mock Telegram update...");
  
  const mockUpdate = {
    update_id: 123456789,
    message: {
      message_id: 1,
      from: {
        id: 123456789,
        is_bot: false,
        first_name: "Test",
        username: "testuser",
        language_code: "en"
      },
      chat: {
        id: 123456789,
        first_name: "Test",
        username: "testuser",
        type: "private"
      },
      date: Math.floor(Date.now() / 1000),
      text: "/start"
    }
  };
  
  try {
    const response = await fetch(WEBHOOK_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(mockUpdate),
    });
    
    if (response.ok) {
      console.log("✅ Mock webhook update processed successfully!");
      console.log(`Status: ${response.status}`);
    } else {
      console.error("❌ Mock webhook update failed:");
      console.error(`Status: ${response.status}`);
      const text = await response.text();
      console.error("Response:", text);
    }
  } catch (error) {
    console.error("❌ Error testing webhook with mock update:", error);
  }
}

async function main() {
  console.log("🚀 Starting webhook tests...");
  console.log("=" .repeat(50));
  
  await testWebhookHealth();
  console.log("-".repeat(50));
  await testWebhookWithMockUpdate();
  
  console.log("=" .repeat(50));
  console.log("✅ Webhook tests completed!");
  console.log("");
  console.log("📝 Note: The mock update test may show errors in the logs");
  console.log("   because it's not a real Telegram update, but it tests");
  console.log("   that the endpoint is reachable and processing requests.");
}

// Run the main function
main().catch(console.error);
