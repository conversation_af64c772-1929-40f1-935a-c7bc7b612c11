# CryBaby - Telegram Image Generation Bot with Hybrid Rate Limiting

A sophisticated Telegram bot that generates images using OpenAI's DALL-E 3, featuring a dual-layer rate limiting system (global + per-user) and comprehensive admin panel.

## 🚀 User Flow Overview

### For Telegram Users

1. **Starting the Bot**
   - User sends `/start` to the bot
   - <PERSON><PERSON> responds with welcome message and available commands
   - No registration needed - users are automatically created on first use

2. **Generating Images**
   - User sends `/generate [prompt]` (e.g., `/generate a cat in space`)
   - <PERSON><PERSON> checks BOTH rate limits:
     - **User limit**: Individual daily quota (default: 10 images/day)
     - **Global limit**: System-wide capacity (default: 100 images/day)
   - If both limits have capacity:
     - <PERSON><PERSON> shows "🎨 Generating your image, please wait..."
     - Image is generated via OpenAI DALL-E 3
     - <PERSON><PERSON> automatically adds CryBaby watermark to the image
     - <PERSON><PERSON> sends the watermarked image with remaining limits info
   - If either limit is exceeded:
     - User limit exceeded: "🚫 Your daily limit exceeded! You've used all 10 of your daily image generations."
     - Global limit exceeded: "🚫 System daily limit exceeded! The global daily capacity of 100 images has been reached."

3. **Checking Limits**
   - User sends `/limit`
   - <PERSON><PERSON> shows both personal and global limits:
     ```
     📊 Generation Limits Status

     👤 Your Personal Limit:
     • Daily limit: 10 images
     • Remaining: 7 images

     🌐 Global System Limit:
     • Daily capacity: 100 images
     • Remaining: 73 images

     • Resets: Daily at midnight UTC
     • Both limits must have capacity for generation
     ```

4. **Getting Help**
   - User sends `/help`
   - Bot shows all available commands and examples

### For Administrators

1. **Accessing Admin Panel**
   - Navigate to `https://your-domain.com/admin`
   - Authenticate using Supabase Auth
   - Dashboard shows system overview

2. **Dashboard View**
   - **Global Status**: Current global capacity, utilization percentage, system status
   - **User Metrics**: Total users, active today, success rates
   - **Generation Stats**: Total generations, today's count, weekly trends
   - **Activity Graph**: Daily breakdown of successful/failed generations

3. **Managing Global Limits**
   - Navigate to "Global Limits" in sidebar
   - View current global settings:
     - Daily limit and remaining capacity
     - Utilization percentage
     - Last reset timestamp
   - Update global capacity:
     - Enter new daily limit
     - System shows impact preview
     - Save changes (immediately effective)

4. **Managing User Limits**
   - Navigate to "User Limits" in sidebar
   - View all users in a table:
     - User ID, daily limit, remaining, last reset
   - Edit individual user limits:
     - Click edit icon
     - Update daily limit
     - Changes apply immediately

5. **Viewing Logs**
   - Navigate to "Logs" in sidebar
   - Filter by:
     - User ID
     - Success/Failed status
   - View details:
     - Timestamp, prompt, status
     - Generated image links
     - Error messages for failures
   - Pagination for large datasets

## 🔄 System Flow Diagram

```
User sends /generate command
            ↓
    Bot receives request
            ↓
    Check user exists?
       ↙        ↘
     No          Yes
      ↓           ↓
Create user    Continue
      ↘         ↙
        ↓
Check daily reset needed?
        ↓
Reset if new day (automatic)
        ↓
Check BOTH limits atomically
        ↓
    Both have capacity?
       ↙        ↘
     No          Yes
      ↓           ↓
Send limit      Generate
exceeded msg    image
                  ↓
              Success?
             ↙      ↘
           No        Yes
           ↓          ↓
      Log fail    Decrement
                  both limits
                      ↓
                  Send image
                  with stats
```

## 🛡️ Rate Limiting Logic

### Hybrid System Design

The bot uses a **dual-layer rate limiting system**:

1. **Global Limit** (System-wide)
   - Protects infrastructure from overload
   - Shared across all users
   - Admin configurable (default: 100/day)
   - Prevents system abuse

2. **Per-User Limit** (Individual)
   - Ensures fair usage distribution
   - Prevents single user from consuming all capacity
   - Individually configurable (default: 10/day)
   - Promotes equitable access

### How It Works

- **Both limits must pass** for generation to proceed
- Limits are checked **atomically** to prevent race conditions
- Decrements happen **only after successful generation**
- Failed generations **don't count** against limits
- Automatic **daily reset** at midnight UTC

### Example Scenarios

1. **Normal Operation**
   - User has 5/10 remaining, Global has 50/100 remaining
   - ✅ Generation proceeds

2. **User Limit Exceeded**
   - User has 0/10 remaining, Global has 50/100 remaining
   - ❌ Generation blocked (user limit)

3. **Global Limit Exceeded**
   - User has 5/10 remaining, Global has 0/100 remaining
   - ❌ Generation blocked (global limit)

## 🎨 Watermarking System

The bot automatically adds a CryBaby logo watermark to all generated images.

### Features
- **Automatic watermarking**: Every generated image includes the CryBaby logo
- **Configurable positioning**: Bottom-right, bottom-left, top-right, top-left, or center
- **Adjustable opacity**: Semi-transparent overlay that doesn't obstruct the image
- **Responsive scaling**: Logo size adapts to image dimensions
- **Fallback handling**: If watermarking fails, original image is sent

### Configuration

Watermarking can be configured via environment variables:

```env
WATERMARK_ENABLED=true              # Enable/disable watermarking
WATERMARK_POSITION=bottom-right     # Logo position
WATERMARK_OPACITY=0.8               # Logo opacity (0-1)
WATERMARK_PADDING=20                # Padding from edges (pixels)
WATERMARK_SCALE=0.12                # Logo size relative to image
```

### Technical Implementation
- Uses **Sharp** library for high-performance image processing
- Programmatically generated logo with gradient and shadow effects
- Watermarked images stored in Supabase Storage
- Atomic operations ensure consistency

## Tech Stack

- **Bot**: TypeScript, Node.js, Grammy (Telegram bot framework), Sharp (image processing)
- **Admin Panel**: Next.js 15, React, Tailwind CSS, shadcn/ui
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **AI**: OpenAI DALL-E 3
- **Runtime**: Bun

## Setup Instructions

### 1. Clone and Install Dependencies

```bash
git clone [your-repo-url]
cd crybaby
bun install
```

### 2. Set up Environment Variables

Copy `.env.example` to `.env.local` and fill in your credentials:

```env
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_KEY=your-supabase-service-key
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
OPENAI_API_KEY=your-openai-api-key
```

### 3. Set up Supabase Database

1. Create a new Supabase project
2. Go to the SQL Editor
3. Run the SQL script from `database/complete_schema.sql`

This will create:
- `cry_global_settings` table for system-wide limits
- `cry_user_limits` table for per-user rate limiting
- `cry_generation_logs` table for audit logs
- PostgreSQL functions for atomic limit checking
- Row Level Security policies for all tables

### 4. Set up Telegram Bot

1. Message @BotFather on Telegram
2. Create a new bot with `/newbot`
3. Get your bot token and add it to `.env.local`
4. Register bot commands for the command menu:
   ```bash
   bun run register-commands
   ```
   This will register `/start`, `/generate`, `/limit`, and `/help` commands so they appear in the Telegram command menu.

### 5. Get OpenAI API Key

1. Go to [OpenAI Platform](https://platform.openai.com/)
2. Create an API key
3. Add it to `.env.local`

### 6. Run the Applications

#### Start the Admin Panel:
```bash
bun run dev
```

#### Start the Telegram Bot:
```bash
cd bot
bun install
bun run dev
```

## Project Structure

```
/
├── app/                    # Next.js app directory
│   ├── admin/             # Admin panel pages
│   ├── api/               # API routes
│   ├── auth/              # Authentication pages
│   └── protected/         # Protected routes
├── bot/                   # Telegram bot code
│   ├── src/               # Bot source code
│   └── utils/             # Bot utilities
├── components/            # React components
│   └── ui/                # shadcn/ui components
├── database/              # Database schemas and docs
├── lib/                   # Shared utilities
└── docs/                  # Documentation
```

## 💡 Usage

### For End Users (Telegram)
1. Start a chat with your bot
2. Send `/start` to see available commands
3. Use `/generate [prompt]` to create images
4. Check remaining limits with `/limit` (shows both user and global)

### For Admins (Web Panel)
1. Log in to the admin panel at `/admin`
2. **Dashboard**: View system-wide analytics and health
3. **Global Limits**: Manage system capacity
4. **User Limits**: Manage individual user quotas
5. **Logs**: Monitor all generation activity

## 📡 API Endpoints

### Admin Panel APIs
- `GET /api/admin/stats` - Dashboard statistics (global & user metrics)
- `GET /api/admin/global` - Get global limit settings
- `POST /api/admin/global` - Update global daily limit
- `GET /api/admin/users` - List all user limits
- `POST /api/admin/users` - Update individual user limits
- `GET /api/admin/logs` - Generation logs with filtering

## 🗄️ Database Schema

### cry_global_settings
- `id` (INTEGER): Always 1 (single row)
- `daily_limit` (INTEGER): System-wide daily capacity
- `remaining` (INTEGER): System-wide remaining capacity
- `last_reset` (DATE): Last reset date

### cry_user_limits
- `user_id` (BIGINT): Telegram user ID
- `daily_limit` (INTEGER): Max generations per day per user
- `remaining` (INTEGER): Remaining generations today
- `last_reset` (DATE): Last reset date

### cry_generation_logs
- `id` (UUID): Unique log ID
- `user_id` (BIGINT): Telegram user ID
- `prompt` (TEXT): Generation prompt
- `timestamp` (TIMESTAMP): Request time
- `success` (BOOLEAN): Success status
- `image_url` (TEXT): Generated image URL
- `error_message` (TEXT): Error details (if failed)

### Key Database Functions

- `check_generation_allowed(user_id)`: Atomically checks both limits
- `decrement_both_limits(user_id)`: Atomically decrements both counters
- `check_and_reset_global_daily_limit()`: Auto-resets global limit
- `check_and_reset_user_daily_limit(user_id)`: Auto-resets user limit

## 🔐 Security Features

- Row Level Security (RLS) on all tables
- Service role key only for bot operations
- Authenticated access required for admin panel
- Input validation and sanitization
- Dual-layer rate limiting prevents abuse
- Atomic operations prevent race conditions
- No sensitive data exposed in logs

## Deployment

### Bot Deployment
- Deploy on any Node.js hosting (Railway, Render, etc.)
- Set environment variables
- Ensure continuous uptime

### Admin Panel Deployment
- Deploy on Vercel (recommended)
- Set environment variables
- Configure domain if needed

## 📊 Admin Operations

### Daily Management

1. **Monitor Global Capacity**
   - Check utilization percentage on dashboard
   - Adjust limits based on demand patterns
   - Watch for depletion during peak hours

2. **User Management**
   - Identify heavy users in logs
   - Adjust individual limits as needed
   - Handle support requests for limit increases

3. **System Health**
   - Review success rates
   - Check error logs for patterns
   - Monitor response times

### Emergency Procedures

1. **System Overload**
   ```sql
   -- Temporarily disable all generation
   UPDATE cry_global_settings SET remaining = 0;
   ```

2. **Reset Everything**
   ```sql
   -- Reset both global and all user limits
   UPDATE cry_global_settings SET remaining = daily_limit;
   UPDATE cry_user_limits SET remaining = daily_limit;
   ```

3. **Increase Capacity**
   - Use admin panel Global Limits page
   - Or run SQL: `UPDATE cry_global_settings SET daily_limit = 200, remaining = 200;`

## 🔍 Monitoring & Analytics

### Key Metrics to Track

- **Global Utilization**: % of daily capacity used
- **Active Users**: Unique users generating today
- **Success Rate**: Successful vs failed generations
- **Peak Hours**: When most generations occur
- **User Distribution**: Heavy vs light users

### SQL Queries for Analysis

```sql
-- System capacity report
SELECT * FROM cry_global_settings;

-- Top users today
SELECT user_id, COUNT(*) as generations 
FROM cry_generation_logs 
WHERE timestamp >= CURRENT_DATE AND success = true 
GROUP BY user_id 
ORDER BY generations DESC;

-- Hourly usage pattern
SELECT EXTRACT(HOUR FROM timestamp) as hour, COUNT(*) 
FROM cry_generation_logs 
WHERE timestamp >= CURRENT_DATE 
GROUP BY hour ORDER BY hour;
```

## 🛠️ Troubleshooting

### Common Issues

1. **"Bot not responding"**
   - Check bot process is running
   - Verify Telegram token is correct
   - Check Supabase connection

2. **"Limits not resetting"**
   - System uses UTC timezone
   - Check `last_reset` dates in database
   - Manual reset: Use admin panel or SQL queries

3. **"Generation fails but limits decrease"**
   - This shouldn't happen (atomic operations)
   - Check database functions are intact
   - Verify bot is using correct functions

4. **"Global limit blocks all users"**
   - Expected behavior when system capacity reached
   - Increase global limit via admin panel
   - Consider scaling infrastructure

### Debug Commands

```bash
# Check bot logs
journalctl -u telegram-bot -f

# Test database connection
bunx supabase db test

# Verify rate limit function
SELECT check_generation_allowed(123456789);

# Check system integrity
SELECT * FROM cry_global_settings;
SELECT COUNT(*) FROM cry_user_limits;
```

## 📈 Scaling Considerations

1. **Increase Global Limit**: Based on OpenAI quota and budget
2. **Adjust User Limits**: Balance fairness vs power users
3. **Add Caching**: For repeated prompts (optional)
4. **Queue System**: For high demand periods (optional)
5. **Multiple Bot Instances**: All share same database

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Test thoroughly (especially rate limiting logic)
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

## 📄 License

MIT License - see LICENSE file for details

## 🙏 Acknowledgments

- Built with [Grammy](https://grammy.dev/) - The Telegram Bot Framework
- UI components from [shadcn/ui](https://ui.shadcn.com/)
- Database powered by [Supabase](https://supabase.com/)
- Images generated via [OpenAI DALL-E 3](https://openai.com/dall-e-3)