# Telegram Bot Webhook Setup

This document explains how to set up the Telegram bot webhook for the CryBaby image generation bot.

## Overview

The bot has been configured to use webhooks instead of long polling for better performance and reliability when deployed to Vercel. The webhook endpoint is located at `/api/webhook/telegram`.

## Webhook URL

Your webhook URL is: `https://crybaby-one.vercel.app/api/webhook/telegram`

## Setup Instructions

### 1. Environment Configuration

Make sure your `.env.local` file contains the webhook URL:

```env
WEBHOOK_URL=https://crybaby-one.vercel.app/api/webhook/telegram
```

### 2. Set Up the Webhook

Run the webhook setup script to configure Telegram to send updates to your webhook:

```bash
bun run setup-webhook
```

This script will:
- Delete any existing webhook
- Set the new webhook URL
- Verify the webhook configuration
- Display bot information

### 3. Deploy to Vercel

Deploy your application to Vercel:

```bash
vercel --prod
```

Or push to your main branch if you have automatic deployments configured.

### 4. Test the Webhook

You can test the webhook endpoint:

```bash
# Test the health endpoint
bun run test-webhook

# Or manually test with curl
curl https://crybaby-one.vercel.app/api/webhook/telegram
```

## Webhook Features

### Logging
The webhook includes comprehensive logging to help with debugging:
- All incoming requests are logged
- User actions and commands are tracked
- Image generation attempts and results are logged
- Error conditions are captured

### Commands Supported
- `/start` - Welcome message and command list
- `/help` - Detailed help information
- `/limit` - Check remaining generation limits
- `/generate [prompt]` - Generate an image

### Error Handling
- Graceful error handling for all operations
- User-friendly error messages
- Automatic logging of failed operations
- Rate limit enforcement

## Monitoring

### Vercel Logs
Check your Vercel deployment logs to monitor webhook activity:
1. Go to your Vercel dashboard
2. Select your project
3. Go to the "Functions" tab
4. Click on the webhook function to see logs

### Telegram Webhook Info
You can check webhook status using the Telegram Bot API:

```bash
curl https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getWebhookInfo
```

## Troubleshooting

### Common Issues

1. **Webhook not receiving updates**
   - Check that the webhook URL is correct
   - Verify the bot token is valid
   - Ensure the deployment is successful

2. **500 errors in webhook**
   - Check Vercel function logs
   - Verify environment variables are set
   - Check database connectivity

3. **Bot not responding**
   - Verify the webhook is set correctly
   - Check for any pending updates blocking the queue
   - Test with a simple `/start` command

### Debug Commands

```bash
# Check webhook status
curl https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getWebhookInfo

# Delete webhook (for testing)
curl -X POST https://api.telegram.org/bot<YOUR_BOT_TOKEN>/deleteWebhook

# Test webhook endpoint
curl https://crybaby-one.vercel.app/api/webhook/telegram
```

## Development

### Local Testing

For local development, you can use ngrok to expose your local server:

1. Install ngrok: `npm install -g ngrok`
2. Start your local server: `bun run dev`
3. Expose it: `ngrok http 3000`
4. Update `WEBHOOK_URL` in `.env.local` with the ngrok URL
5. Run `bun run setup-webhook` to update the webhook

### Switching Back to Polling

If you need to switch back to polling mode:

1. Delete the webhook: `curl -X POST https://api.telegram.org/bot<YOUR_BOT_TOKEN>/deleteWebhook`
2. Use the original bot code with `bot.start()`

## Security

The webhook endpoint includes basic security measures:
- Validates incoming request format
- Handles malformed requests gracefully
- Logs suspicious activity
- Rate limiting through the application logic

For production, consider adding:
- Webhook secret token validation
- IP allowlisting for Telegram servers
- Request rate limiting at the infrastructure level

## Performance

The webhook approach provides several benefits:
- Lower latency for user interactions
- Better resource utilization
- Automatic scaling with Vercel
- No need for persistent connections

## Support

If you encounter issues:
1. Check the Vercel function logs
2. Verify webhook configuration with Telegram
3. Test the endpoint directly
4. Review environment variables

The webhook is designed to be robust and self-healing, with comprehensive logging to help diagnose any issues.
