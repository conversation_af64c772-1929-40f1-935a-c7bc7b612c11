#!/usr/bin/env bun

/**
 * Test to verify the environment configuration is working with new YouTube-style settings
 */

import { addWatermarkFromUrl, getWatermarkConfig } from '../lib/bot/utils/watermark';
import sharp from 'sharp';
import fs from 'fs/promises';
import path from 'path';

const TEST_OUTPUT_DIR = './tests/output';

async function testEnvironmentConfig() {
  console.log('🔧 Testing Environment Configuration...\n');
  
  try {
    // Ensure output directory exists
    await fs.mkdir(TEST_OUTPUT_DIR, { recursive: true });
    
    // Get current environment config
    const config = getWatermarkConfig();
    console.log('⚙️ Environment watermark config:');
    console.log('  Position:', config.position);
    console.log('  Opacity:', config.opacity);
    console.log('  Padding:', config.padding, 'px');
    console.log('  Scale:', config.scale, `(${(config.scale * 100).toFixed(1)}% of image width)`);
    
    // Create a test image
    console.log('\n🖼️ Creating 1024x1024 test image...');
    const testImage = await sharp({
      create: {
        width: 1024,
        height: 1024,
        channels: 3,
        background: { r: 70, g: 130, b: 180 }
      }
    })
    .composite([
      {
        input: Buffer.from(`
          <svg width="1024" height="1024">
            <defs>
              <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#ff9a9e;stop-opacity:0.8" />
                <stop offset="100%" style="stop-color:#fecfef;stop-opacity:0.8" />
              </linearGradient>
            </defs>
            <rect width="100%" height="100%" fill="url(#grad)" />
            <circle cx="512" cy="512" r="200" fill="rgba(255,255,255,0.3)" />
            <text x="512" y="512" text-anchor="middle" font-family="Arial" font-size="48" fill="white" font-weight="bold">
              YouTube Style
            </text>
            <text x="512" y="560" text-anchor="middle" font-family="Arial" font-size="24" fill="white" opacity="0.8">
              Watermark Test
            </text>
          </svg>
        `),
        top: 0,
        left: 0
      }
    ])
    .png()
    .toBuffer();
    
    // Apply watermark using environment config
    console.log('🎨 Applying watermark with environment config...');
    const watermarkedImage = await addWatermarkFromUrl('data:image/png;base64,' + testImage.toString('base64'));
    
    // Save the result
    const outputPath = path.join(TEST_OUTPUT_DIR, 'env-config-watermarked.png');
    await fs.writeFile(outputPath, watermarkedImage);
    
    // Calculate expected logo dimensions
    const expectedLogoWidth = Math.floor(1024 * config.scale);
    const expectedLogoHeight = Math.floor(expectedLogoWidth * 0.3);
    const expectedLeft = 1024 - expectedLogoWidth - config.padding;
    const expectedTop = 1024 - expectedLogoHeight - config.padding;
    
    console.log('\n📐 Expected logo dimensions:');
    console.log('  Width:', expectedLogoWidth, 'px');
    console.log('  Height:', expectedLogoHeight, 'px');
    console.log('  Position: (', expectedLeft, ',', expectedTop, ')');
    console.log('  Logo area:', ((expectedLogoWidth * expectedLogoHeight) / (1024 * 1024) * 100).toFixed(3), '% of image');
    
    console.log('\n✅ Environment config test completed!');
    console.log('💾 Watermarked image saved to:', outputPath);
    
    // Test with a real image URL too
    console.log('\n🌐 Testing with real image URL...');
    const realImageUrl = 'https://picsum.photos/1024/1024';
    const realWatermarked = await addWatermarkFromUrl(realImageUrl);
    
    const realOutputPath = path.join(TEST_OUTPUT_DIR, 'env-config-real-image.png');
    await fs.writeFile(realOutputPath, realWatermarked);
    console.log('✅ Real image watermarked and saved to:', realOutputPath);
    
    console.log('\n🎉 All environment config tests passed!');
    console.log('📁 Check the output directory for results');
    
  } catch (error) {
    console.error('❌ Environment config test failed:', error);
  }
}

// Run if executed directly
if (import.meta) testEnvironmentConfig();

export { testEnvironmentConfig };
